# XPrinter 统一数据管理策略

## 📋 设计原则

基于简化的用户体系，设计统一的数据生命周期管理策略，所有用户享受相同的数据保留和恢复服务。

## 🎯 核心策略

### 统一数据保留期限
```
📊 所有用户统一策略：
├── 可恢复期: 6个月（180天）
├── 归档期: 12个月（365天） 
└── 永久删除: 18个月（540天）
```

### 数据状态流转
```
🔄 数据生命周期：
├── 0-6个月: 可查询可恢复（热存储）
├── 6-12个月: 可查询不可恢复（温存储）
├── 12-18个月: 归档状态，仅管理员可见（冷存储）
└── 18个月+: 永久删除
```

## 🗄️ 数据库结构设计

### 1. 简化的字段设计
```sql
-- 为现有表添加统一的生命周期字段
ALTER TABLE templet ADD COLUMN (
    archiveTime DATETIME NULL COMMENT '归档时间',
    INDEX idx_delete_time (deleteTime),
    INDEX idx_archive_time (archiveTime)
);

ALTER TABLE templet_group ADD COLUMN (
    archiveTime DATETIME NULL COMMENT '归档时间',
    INDEX idx_delete_time (deleteTime),
    INDEX idx_archive_time (archiveTime)
);

ALTER TABLE cloudfile ADD COLUMN (
    archiveTime DATETIME NULL COMMENT '归档时间',
    INDEX idx_delete_time (deleteTime),
    INDEX idx_archive_time (archiveTime)
);
```

### 2. 数据清理日志表
```sql
CREATE TABLE data_cleanup_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    dataType VARCHAR(50) NOT NULL COMMENT '数据类型：template/template_group/cloudfile',
    targetId INT NOT NULL COMMENT '目标数据ID',
    targetName VARCHAR(255) COMMENT '目标数据名称',
    operation ENUM('archive', 'purge') NOT NULL COMMENT '操作类型',
    operationTime DATETIME NOT NULL COMMENT '操作时间',
    batchId VARCHAR(50) COMMENT '批次ID',
    affectedCount INT DEFAULT 1 COMMENT '影响数据数量',
    INDEX idx_user_time (userId, operationTime),
    INDEX idx_operation_time (operation, operationTime),
    INDEX idx_batch (batchId)
) COMMENT '数据清理操作日志';
```

## ⚙️ 定时任务实现

### 1. 统一数据生命周期管理器
```java
package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
public class UnifiedDataLifecycleManager {

    // 统一的数据保留期限
    private static final int RECOVERY_PERIOD_DAYS = 180;  // 6个月可恢复期
    private static final int ARCHIVE_PERIOD_DAYS = 365;   // 12个月归档期
    private static final int PURGE_PERIOD_DAYS = 540;     // 18个月后永久删除

    /**
     * 每天凌晨2点执行数据归档任务
     * 将超过6个月的已删除数据标记为归档状态
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void archiveExpiredData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行统一数据归档任务，批次ID: " + batchId);

        try {
            int totalArchived = 0;
            
            // 归档模板数据
            totalArchived += archiveDataByTable("templet", batchId);
            
            // 归档分组数据
            totalArchived += archiveDataByTable("templet_group", batchId);
            
            // 归档文件数据
            totalArchived += archiveDataByTable("cloudfile", batchId);

            LogKit.info("数据归档任务完成，批次ID: " + batchId + "，总计归档: " + totalArchived + " 条数据");
        } catch (Exception e) {
            LogKit.error("数据归档任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点执行数据永久删除任务
     * 永久删除超过18个月的数据
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void purgeExpiredData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行统一数据永久删除任务，批次ID: " + batchId);

        try {
            int totalPurged = 0;
            
            // 删除模板数据
            totalPurged += purgeDataByTable("templet", batchId);
            
            // 删除分组数据
            totalPurged += purgeDataByTable("templet_group", batchId);
            
            // 删除文件数据
            totalPurged += purgeDataByTable("cloudfile", batchId);

            LogKit.info("数据永久删除任务完成，批次ID: " + batchId + "，总计删除: " + totalPurged + " 条数据");
        } catch (Exception e) {
            LogKit.error("数据永久删除任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每周一上午9点发送数据即将过期提醒
     * 提前7天提醒用户数据即将无法恢复
     */
    @Scheduled(cron = "0 0 9 * * MON")
    public void sendExpirationNotifications() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始发送数据过期提醒，批次ID: " + batchId);

        try {
            // 查找即将过期的用户数据（还有7天就要归档）
            int warningDays = RECOVERY_PERIOD_DAYS - 7;  // 173天
            List<Record> usersWithExpiringData = findUsersWithExpiringData(warningDays);

            int notificationCount = 0;
            for (Record user : usersWithExpiringData) {
                try {
                    sendExpirationNotification(user);
                    logCleanupOperation(user.getInt("userId"), "notification", 0, 
                                     "数据过期提醒", "notify", 
                                     "即将过期数据: " + user.getInt("expiring_count") + "个", 
                                     batchId, 1);
                    notificationCount++;
                } catch (Exception e) {
                    LogKit.error("发送通知失败，用户ID: " + user.getInt("userId"), e);
                }
            }

            LogKit.info("数据过期提醒发送完成，批次ID: " + batchId + "，通知用户: " + notificationCount + " 个");
        } catch (Exception e) {
            LogKit.error("发送数据过期提醒失败: " + e.getMessage(), e);
        }
    }

    /**
     * 归档指定表的过期数据
     */
    private int archiveDataByTable(String tableName, String batchId) {
        String sql = "UPDATE " + tableName + " " +
                    "SET archiveTime = NOW() " +
                    "WHERE deleteTime IS NOT NULL " +
                    "AND archiveTime IS NULL " +
                    "AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        int affectedRows = Db.update(sql, RECOVERY_PERIOD_DAYS);

        if (affectedRows > 0) {
            LogKit.info("归档 " + tableName + " 表数据: " + affectedRows + " 条");
            logCleanupOperation(0, tableName, 0, "批量归档", "archive", 
                             "超过" + RECOVERY_PERIOD_DAYS + "天的已删除数据", 
                             batchId, affectedRows);
        }

        return affectedRows;
    }

    /**
     * 永久删除指定表的归档数据
     */
    private int purgeDataByTable(String tableName, String batchId) {
        // 先查询要删除的数据，用于日志记录
        String selectSql = "SELECT id, name, userId FROM " + tableName + " " +
                          "WHERE deleteTime IS NOT NULL " +
                          "AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        List<Record> toDelete = Db.find(selectSql, PURGE_PERIOD_DAYS);

        if (toDelete.isEmpty()) {
            return 0;
        }

        // 执行删除
        String deleteSql = "DELETE FROM " + tableName + " " +
                          "WHERE deleteTime IS NOT NULL " +
                          "AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        int affectedRows = Db.update(deleteSql, PURGE_PERIOD_DAYS);

        if (affectedRows > 0) {
            LogKit.info("永久删除 " + tableName + " 表数据: " + affectedRows + " 条");
            
            // 记录删除日志（批量记录）
            logCleanupOperation(0, tableName, 0, "批量永久删除", "purge", 
                             "超过" + PURGE_PERIOD_DAYS + "天的已删除数据", 
                             batchId, affectedRows);
        }

        return affectedRows;
    }

    /**
     * 查找有即将过期数据的用户
     */
    private List<Record> findUsersWithExpiringData(int warningDays) {
        String sql = "SELECT u.userId, u.userPhone, COUNT(*) as expiring_count " +
                    "FROM user u " +
                    "WHERE EXISTS (" +
                    "  SELECT 1 FROM templet t WHERE t.userId = u.userId " +
                    "    AND t.deleteTime IS NOT NULL AND t.archiveTime IS NULL " +
                    "    AND t.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)" +
                    ") OR EXISTS (" +
                    "  SELECT 1 FROM templet_group tg WHERE tg.userId = u.userId " +
                    "    AND tg.deleteTime IS NOT NULL AND tg.archiveTime IS NULL " +
                    "    AND tg.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)" +
                    ") OR EXISTS (" +
                    "  SELECT 1 FROM cloudfile cf WHERE cf.userId = u.userId " +
                    "    AND cf.deleteTime IS NOT NULL AND cf.archiveTime IS NULL " +
                    "    AND cf.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)" +
                    ") " +
                    "GROUP BY u.userId, u.userPhone";

        return Db.find(sql, warningDays, warningDays, warningDays);
    }

    /**
     * 发送过期通知
     */
    private void sendExpirationNotification(Record user) {
        // TODO: 实现具体的通知发送逻辑
        // 可以集成邮件服务、短信服务、站内消息等
        LogKit.info("发送数据过期提醒给用户: " + user.getInt("userId") + 
                   ", 即将过期数据: " + user.getInt("expiring_count") + "个");
        
        // 示例：发送站内消息
        // MessageService.sendMessage(user.getInt("userId"), 
        //     "数据过期提醒", 
        //     "您有" + user.getInt("expiring_count") + "个已删除的文件将在7天后无法恢复，请及时处理。");
    }

    /**
     * 记录清理操作日志
     */
    private void logCleanupOperation(Integer userId, String dataType, Integer targetId, 
                                   String targetName, String operation, String reason, 
                                   String batchId, int affectedCount) {
        String sql = "INSERT INTO data_cleanup_logs " +
                    "(userId, dataType, targetId, targetName, operation, operationTime, reason, batchId, affectedCount) " +
                    "VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?)";

        Db.update(sql, userId, dataType, targetId, targetName, operation, reason, batchId, affectedCount);
    }

    /**
     * 手动触发数据清理（管理员功能）
     */
    public String manualCleanup(String operation) {
        String batchId = "MANUAL_" + UUID.randomUUID().toString();
        LogKit.info("手动触发数据清理: " + operation + ", 批次ID: " + batchId);

        try {
            int totalProcessed = 0;
            
            if ("archive".equals(operation)) {
                totalProcessed += archiveDataByTable("templet", batchId);
                totalProcessed += archiveDataByTable("templet_group", batchId);
                totalProcessed += archiveDataByTable("cloudfile", batchId);
                LogKit.info("手动归档完成，处理数据: " + totalProcessed + " 条");
            } else if ("purge".equals(operation)) {
                totalProcessed += purgeDataByTable("templet", batchId);
                totalProcessed += purgeDataByTable("templet_group", batchId);
                totalProcessed += purgeDataByTable("cloudfile", batchId);
                LogKit.info("手动永久删除完成，处理数据: " + totalProcessed + " 条");
            }
            
            return "操作成功，批次ID: " + batchId + "，处理数据: " + totalProcessed + " 条";
        } catch (Exception e) {
            LogKit.error("手动数据清理失败: " + e.getMessage(), e);
            throw new RuntimeException("数据清理失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据清理统计信息
     */
    public Record getCleanupStatistics() {
        String sql = "SELECT " +
                    "  SUM(CASE WHEN operation = 'archive' THEN affectedCount ELSE 0 END) as total_archived, " +
                    "  SUM(CASE WHEN operation = 'purge' THEN affectedCount ELSE 0 END) as total_purged, " +
                    "  COUNT(DISTINCT CASE WHEN operation = 'notify' THEN userId END) as notified_users, " +
                    "  MAX(operationTime) as last_operation_time " +
                    "FROM data_cleanup_logs " +
                    "WHERE operationTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)";

        return Db.findFirst(sql);
    }
}
```

## 📊 API接口调整

### 1. 查询接口过滤归档数据
```java
// 修改DataRecoveryService中的查询方法
public RetKit getDeletedDataList(Integer userId, String dataType, int pageNumber, int pageSize, 
                               String startDate, String endDate) {
    // ... 原有验证逻辑 ...

    StringBuilder sql = new StringBuilder();
    sql.append("SELECT ").append(primaryKey).append(" as id, ");
    
    // 根据数据类型选择字段...
    
    sql.append("WHERE userId = ? AND deleteTime IS NOT NULL ");
    sql.append("AND archiveTime IS NULL "); // 只查询未归档的数据
    
    // ... 其他查询逻辑 ...
}
```

### 2. 添加管理员清理接口
```java
// 在DataRecoveryController中添加管理员功能
@OperationLog(modelName = "data_cleanup")
public void manualCleanup() {
    LogKit.info("管理员手动数据清理接口开始--------------------");
    
    String operation = getPara("operation"); // archive 或 purge
    
    // TODO: 添加管理员权限验证
    
    try {
        UnifiedDataLifecycleManager manager = new UnifiedDataLifecycleManager();
        String result = manager.manualCleanup(operation);
        renderJson(RetKit.ok(result));
    } catch (Exception e) {
        LogKit.error("手动数据清理失败: " + e.getMessage(), e);
        renderJson(RetKit.fail("清理失败: " + e.getMessage()));
    }
    
    LogKit.info("管理员手动数据清理接口结束--------------------");
}

public void getCleanupStatistics() {
    LogKit.info("获取数据清理统计接口开始--------------------");
    
    try {
        UnifiedDataLifecycleManager manager = new UnifiedDataLifecycleManager();
        Record stats = manager.getCleanupStatistics();
        renderJson(RetKit.ok("statistics", stats));
    } catch (Exception e) {
        LogKit.error("获取清理统计失败: " + e.getMessage(), e);
        renderJson(RetKit.fail("获取统计失败: " + e.getMessage()));
    }
    
    LogKit.info("获取数据清理统计接口结束--------------------");
}
```

## 📈 监控和报告

### 1. 清理效果统计
```sql
-- 查看最近30天的清理统计
SELECT 
    operation,
    dataType,
    DATE(operationTime) as operation_date,
    SUM(affectedCount) as daily_count
FROM data_cleanup_logs 
WHERE operationTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY operation, dataType, DATE(operationTime)
ORDER BY operation_date DESC;

-- 查看存储空间节省效果
SELECT 
    '归档数据' as category,
    COUNT(*) as count,
    ROUND(SUM(LENGTH(data))/1024/1024, 2) as size_mb
FROM templet 
WHERE archiveTime IS NOT NULL
UNION ALL
SELECT 
    '已删除数据' as category,
    COUNT(*) as count,
    ROUND(SUM(LENGTH(data))/1024/1024, 2) as size_mb
FROM templet 
WHERE deleteTime IS NOT NULL AND archiveTime IS NULL;
```

## 🎯 实施计划

### 阶段一：基础实现（1周）
1. 数据库结构调整
2. 基础定时任务实现
3. 查询接口过滤调整

### 阶段二：完善功能（1周）
1. 用户通知机制
2. 管理员清理接口
3. 统计监控功能

### 阶段三：优化提升（3天）
1. 性能优化
2. 错误处理完善
3. 日志和监控完善

这个统一的策略简化了用户管理，所有用户享受相同的6个月数据恢复期，既保证了用户体验，又有效控制了存储成本。
