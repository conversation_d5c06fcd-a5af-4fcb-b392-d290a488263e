-- XPrinter数据恢复功能 - 软删除功能测试脚本
-- 用于验证软删除机制是否正常工作
--
-- 修复说明：
-- 1. 修正了 user 表的字段名：phone -> userPhone
-- 2. 添加了必要的字段：status, registerType
-- 3. 添加了表结构预检查，避免字段不存在的错误
-- 4. 使用安全的测试ID，避免与现有数据冲突：
--    - user: 500000 (最大ID: 361895)
--    - templet: 5000000 (最大ID: 4438972)
--    - templet_group: 100000 (最大ID: 68009)
--    - cloudfile: 10000 (最大ID: 2812)

-- =====================================================
-- 预检查：验证表结构
-- =====================================================

SELECT '=== 检查 user 表结构 ===' as check_step;

-- 检查 user 表的字段结构，确保字段名正确
SELECT
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许NULL',
    COLUMN_DEFAULT as '默认值'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'user'
  AND COLUMN_NAME IN ('userId', 'userPhone', 'status', 'registerType', 'createTime')
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 测试准备：创建测试数据
-- =====================================================

-- 插入测试用户（如果不存在）
-- 使用安全的测试ID，避免与现有数据冲突
-- user 最大ID: 361895，使用 500000 作为测试ID
-- 添加必要的字段：status（状态）和 registerType（注册类型）
INSERT IGNORE INTO `user` (`userId`, `userPhone`, `status`, `registerType`, `createTime`)
VALUES (500000, '13800000000', 1, 2, NOW());

-- 插入测试模板分组
-- templet_group 最大ID: 68009，使用 100000 作为测试ID
INSERT IGNORE INTO `templet_group` (`id`, `userId`, `name`, `type`, `createTime`)
VALUES (100000, 500000, '测试分组', 2, NOW());

-- 插入测试模板
-- templet 最大ID: 4438972，使用 5000000 作为测试ID
INSERT IGNORE INTO `templet` (`id`, `userId`, `groupId`, `name`, `cover`, `width`, `height`, `data`, `createTime`, `type`)
VALUES (5000000, 500000, 100000, '测试模板', 'test.jpg', 100, 50, '{}', NOW(), 2);

-- 插入测试云端文件
-- cloudfile 最大ID: 2812，使用 10000 作为测试ID
INSERT IGNORE INTO `cloudfile` (`id`, `userId`, `name`, `url`, `type`, `createTime`)
VALUES (10000, 500000, '测试文件.jpg', 'http://test.com/test.jpg', 1, NOW());

-- =====================================================
-- 测试1：验证正常查询（应该能看到测试数据）
-- =====================================================

SELECT '=== 测试1：验证正常查询 ===' as test_step;

-- 查询未删除的模板
SELECT COUNT(*) as template_count, '未删除模板数量' as description
FROM templet
WHERE userId = 500000 AND deleteTime IS NULL;

-- 查询未删除的分组
SELECT COUNT(*) as group_count, '未删除分组数量' as description
FROM templet_group
WHERE userId = 500000 AND deleteTime IS NULL;

-- 查询未删除的文件
SELECT COUNT(*) as file_count, '未删除文件数量' as description
FROM cloudfile
WHERE userId = 500000 AND deleteTime IS NULL;

-- =====================================================
-- 测试2：执行软删除操作
-- =====================================================

SELECT '=== 测试2：执行软删除操作 ===' as test_step;

-- 软删除模板
UPDATE templet SET deleteTime = NOW() WHERE id = 5000000;

-- 软删除分组
UPDATE templet_group SET deleteTime = NOW() WHERE id = 100000;

-- 软删除文件
UPDATE cloudfile SET deleteTime = NOW() WHERE id = 10000;

SELECT '软删除操作完成' as status;

-- =====================================================
-- 测试3：验证软删除后的查询结果
-- =====================================================

SELECT '=== 测试3：验证软删除后的查询结果 ===' as test_step;

-- 查询未删除的数据（应该为0）
SELECT COUNT(*) as template_count, '未删除模板数量（应该为0）' as description
FROM templet
WHERE userId = 500000 AND deleteTime IS NULL;

SELECT COUNT(*) as group_count, '未删除分组数量（应该为0）' as description
FROM templet_group
WHERE userId = 500000 AND deleteTime IS NULL;

SELECT COUNT(*) as file_count, '未删除文件数量（应该为0）' as description
FROM cloudfile
WHERE userId = 500000 AND deleteTime IS NULL;

-- 查询已删除的数据（应该为1）
SELECT COUNT(*) as deleted_template_count, '已删除模板数量（应该为1）' as description
FROM templet
WHERE userId = 500000 AND deleteTime IS NOT NULL;

SELECT COUNT(*) as deleted_group_count, '已删除分组数量（应该为1）' as description
FROM templet_group
WHERE userId = 500000 AND deleteTime IS NOT NULL;

SELECT COUNT(*) as deleted_file_count, '已删除文件数量（应该为1）' as description
FROM cloudfile
WHERE userId = 500000 AND deleteTime IS NOT NULL;

-- =====================================================
-- 测试4：验证已删除数据的详细信息
-- =====================================================

SELECT '=== 测试4：验证已删除数据的详细信息 ===' as test_step;

-- 查看已删除的模板详情
SELECT
    id,
    name,
    deleteTime,
    '已删除模板' as data_type
FROM templet
WHERE userId = 500000 AND deleteTime IS NOT NULL;

-- 查看已删除的分组详情
SELECT
    id,
    name,
    deleteTime,
    '已删除分组' as data_type
FROM templet_group
WHERE userId = 500000 AND deleteTime IS NOT NULL;

-- 查看已删除的文件详情
SELECT
    id,
    name,
    deleteTime,
    '已删除文件' as data_type
FROM cloudfile
WHERE userId = 500000 AND deleteTime IS NOT NULL;

-- =====================================================
-- 测试5：测试数据恢复（取消软删除）
-- =====================================================

SELECT '=== 测试5：测试数据恢复 ===' as test_step;

-- 恢复模板
UPDATE templet SET deleteTime = NULL WHERE id = 5000000;

-- 恢复分组
UPDATE templet_group SET deleteTime = NULL WHERE id = 100000;

-- 恢复文件
UPDATE cloudfile SET deleteTime = NULL WHERE id = 10000;

SELECT '数据恢复操作完成' as status;

-- =====================================================
-- 测试6：验证恢复后的查询结果
-- =====================================================

SELECT '=== 测试6：验证恢复后的查询结果 ===' as test_step;

-- 查询未删除的数据（应该恢复为1）
SELECT COUNT(*) as template_count, '恢复后未删除模板数量（应该为1）' as description
FROM templet
WHERE userId = 500000 AND deleteTime IS NULL;

SELECT COUNT(*) as group_count, '恢复后未删除分组数量（应该为1）' as description
FROM templet_group
WHERE userId = 500000 AND deleteTime IS NULL;

SELECT COUNT(*) as file_count, '恢复后未删除文件数量（应该为1）' as description
FROM cloudfile
WHERE userId = 500000 AND deleteTime IS NULL;

-- 查询已删除的数据（应该为0）
SELECT COUNT(*) as deleted_template_count, '恢复后已删除模板数量（应该为0）' as description
FROM templet
WHERE userId = 500000 AND deleteTime IS NOT NULL;

SELECT COUNT(*) as deleted_group_count, '恢复后已删除分组数量（应该为0）' as description
FROM templet_group
WHERE userId = 500000 AND deleteTime IS NOT NULL;

SELECT COUNT(*) as deleted_file_count, '恢复后已删除文件数量（应该为0）' as description
FROM cloudfile
WHERE userId = 500000 AND deleteTime IS NOT NULL;

-- =====================================================
-- 测试7：测试索引性能
-- =====================================================

SELECT '=== 测试7：测试索引性能 ===' as test_step;

-- 测试模板查询的执行计划
EXPLAIN SELECT * FROM templet WHERE userId = 500000 AND deleteTime IS NULL ORDER BY createTime DESC LIMIT 10;

-- 测试分组查询的执行计划
EXPLAIN SELECT * FROM templet_group WHERE userId = 500000 AND deleteTime IS NULL ORDER BY createTime DESC;

-- 测试文件查询的执行计划
EXPLAIN SELECT * FROM cloudfile WHERE userId = 500000 AND deleteTime IS NULL ORDER BY createTime DESC;

-- =====================================================
-- 清理测试数据
-- =====================================================

SELECT '=== 清理测试数据 ===' as test_step;

-- 删除测试数据
DELETE FROM templet WHERE id = 5000000;
DELETE FROM templet_group WHERE id = 100000;
DELETE FROM cloudfile WHERE id = 10000;
DELETE FROM user WHERE userId = 500000;

SELECT '测试数据清理完成' as status;

-- =====================================================
-- 测试总结
-- =====================================================

SELECT '=== 软删除功能测试完成 ===' as test_summary;

SELECT 
    '软删除功能测试完成' as status,
    NOW() as test_time,
    '请检查以上所有测试结果是否符合预期' as note;

-- =====================================================
-- 预期结果说明
-- =====================================================

/*
预期测试结果：

测试1：应该看到各1条测试数据
测试2：软删除操作成功
测试3：
- 未删除数据数量应该都为0
- 已删除数据数量应该都为1
测试4：应该能看到已删除数据的详细信息，包括deleteTime字段
测试5：数据恢复操作成功
测试6：
- 恢复后未删除数据数量应该都为1
- 恢复后已删除数据数量应该都为0
测试7：执行计划应该显示使用了相关索引

如果所有测试结果都符合预期，说明软删除功能工作正常。
*/
