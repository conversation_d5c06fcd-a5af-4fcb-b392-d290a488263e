# 数据恢复API接口文档

## 概述

数据恢复API提供了完整的已删除数据管理功能，包括查询、恢复、永久删除等操作。所有接口都需要用户登录认证。

**基础路径**: `/api/v2/datarecovery`

## 接口列表

### 1. 获取已删除数据列表

**接口地址**: `GET /getDeletedDataList`

**功能描述**: 分页获取用户的已删除数据列表

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dataType | String | 是 | 数据类型：template, template_group, cloudfile |
| pageNumber | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| startDate | String | 否 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 否 | 结束日期，格式：yyyy-MM-dd |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "操作成功",
  "page": {
    "list": [
      {
        "id": 123,
        "name": "测试模板",
        "cover": "http://example.com/cover.jpg",
        "width": 100,
        "height": 50,
        "createTime": "2024-07-01 10:00:00",
        "deleteTime": "2024-07-20 15:30:00",
        "type": 2
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalPage": 1,
    "totalRow": 1
  }
}
```

### 2. 获取已删除数据详情

**接口地址**: `GET /getDeletedDataDetail`

**功能描述**: 获取指定已删除数据的详细信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dataType | String | 是 | 数据类型：template, template_group, cloudfile |
| dataId | String | 是 | 数据ID |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "操作成功",
  "data": {
    "id": 123,
    "name": "测试模板",
    "cover": "http://example.com/cover.jpg",
    "width": 100,
    "height": 50,
    "data": "{}",
    "createTime": "2024-07-01 10:00:00",
    "deleteTime": "2024-07-20 15:30:00",
    "userId": 1001
  }
}
```

### 3. 恢复单个数据

**接口地址**: `POST /recoverSingleData`

**功能描述**: 恢复单个已删除的数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dataType | String | 是 | 数据类型：template, template_group, cloudfile |
| dataId | String | 是 | 数据ID |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "数据恢复成功"
}
```

### 4. 批量恢复数据

**接口地址**: `POST /recoverBatchData`

**功能描述**: 批量恢复多个已删除的数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dataType | String | 是 | 数据类型：template, template_group, cloudfile |
| dataIds | String | 是 | 数据ID列表，逗号分隔，如："123,456,789" |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "批量恢复成功",
  "data": {
    "successCount": 3,
    "failCount": 0,
    "failedIds": []
  }
}
```

### 5. 按时间范围恢复数据

**接口地址**: `POST /recoverDataByDateRange`

**功能描述**: 恢复指定时间范围内删除的数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dataType | String | 是 | 数据类型：template, template_group, cloudfile |
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "批量恢复成功",
  "data": {
    "successCount": 5,
    "failCount": 1,
    "failedIds": ["789"]
  }
}
```

### 6. 永久删除数据

**接口地址**: `POST /permanentDeleteData`

**功能描述**: 永久删除已删除的数据（不可恢复）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dataType | String | 是 | 数据类型：template, template_group, cloudfile |
| dataId | String | 是 | 数据ID |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "数据永久删除成功"
}
```

### 7. 获取恢复统计信息

**接口地址**: `GET /getRecoveryStatistics`

**功能描述**: 获取用户的数据恢复统计信息

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "操作成功",
  "statistics": {
    "deletedTemplateCount": 15,
    "deletedGroupCount": 3,
    "deletedFileCount": 8,
    "totalDeletedCount": 26,
    "recentDeletedCount": 5
  }
}
```

### 8. 获取恢复历史记录

**接口地址**: `GET /getRecoveryHistory`

**功能描述**: 分页获取用户的数据恢复操作历史记录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNumber | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "msg": "操作成功",
  "page": {
    "list": [
      {
        "id": 1,
        "userId": 1001,
        "dataType": "template",
        "targetId": 123,
        "targetName": "测试模板",
        "recoveryType": "single",
        "recoveryStatus": "success",
        "recoveryTime": "2024-07-21 10:30:00",
        "originalDeleteTime": "2024-07-20 15:30:00"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalPage": 1,
    "totalRow": 1
  }
}
```

## 数据类型说明

| 数据类型 | 说明 | 对应表 |
|----------|------|--------|
| template | 用户模板 | templet |
| template_group | 模板分组 | templet_group |
| cloudfile | 云端文件 | cloudfile |

## 恢复类型说明

| 恢复类型 | 说明 |
|----------|------|
| single | 单个恢复 |
| batch | 批量恢复 |
| batch_by_date | 按时间范围批量恢复 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 501 | 操作失败 |

## 注意事项

1. 所有接口都需要用户登录认证
2. 用户只能操作自己的数据
3. 已删除的数据才能进行恢复操作
4. 永久删除操作不可逆，请谨慎使用
5. 批量操作会记录成功和失败的详细信息
6. 所有恢复操作都会记录到操作日志中
