openapi: 3.0.3
info:
  title: XPrinter 数据恢复 API
  description: |
    XPrinter 数据恢复功能 API 接口文档
    
    提供已删除数据的查询、恢复、永久删除等功能。
    
    支持的数据类型：
    - template: 用户模板
    - template_group: 模板分组  
    - cloudfile: 云端文件
    
    **注意事项：**
    - 所有接口都需要用户登录认证
    - 用户只能操作自己的数据
    - 已删除的数据才能进行恢复操作
    - 永久删除操作不可逆，请谨慎使用
  version: 1.0.0
  contact:
    name: XPrinter Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v2
    description: 本地开发环境
  - url: https://api.xprinter.com/api/v2
    description: 生产环境

security:
  - BearerAuth: []

paths:
  /datarecovery/getDeletedDataList:
    get:
      tags:
        - 数据查询
      summary: 获取已删除数据列表
      description: 分页获取用户的已删除数据列表，支持时间范围过滤
      parameters:
        - name: dataType
          in: query
          required: true
          description: 数据类型
          schema:
            type: string
            enum: [template, template_group, cloudfile]
            example: template
        - name: pageNumber
          in: query
          required: false
          description: 页码，默认1
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: pageSize
          in: query
          required: false
          description: 每页大小，默认10
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: startDate
          in: query
          required: false
          description: 开始日期，格式：yyyy-MM-dd
          schema:
            type: string
            format: date
            example: "2024-07-01"
        - name: endDate
          in: query
          required: false
          description: 结束日期，格式：yyyy-MM-dd
          schema:
            type: string
            format: date
            example: "2024-07-21"
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResponse'
              example:
                success: true
                code: "200"
                msg: "操作成功"
                page:
                  list:
                    - id: 123
                      name: "测试模板"
                      cover: "http://example.com/cover.jpg"
                      width: 100
                      height: 50
                      createTime: "2024-07-01 10:00:00"
                      deleteTime: "2024-07-20 15:30:00"
                      type: 2
                  pageNumber: 1
                  pageSize: 10
                  totalPage: 1
                  totalRow: 1
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/getDeletedDataDetail:
    get:
      tags:
        - 数据查询
      summary: 获取已删除数据详情
      description: 获取指定已删除数据的详细信息
      parameters:
        - name: dataType
          in: query
          required: true
          description: 数据类型
          schema:
            type: string
            enum: [template, template_group, cloudfile]
            example: template
        - name: dataId
          in: query
          required: true
          description: 数据ID
          schema:
            type: string
            example: "123"
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataDetailResponse'
              example:
                success: true
                code: "200"
                msg: "操作成功"
                data:
                  id: 123
                  name: "测试模板"
                  cover: "http://example.com/cover.jpg"
                  width: 100
                  height: 50
                  data: "{}"
                  createTime: "2024-07-01 10:00:00"
                  deleteTime: "2024-07-20 15:30:00"
                  userId: 1001
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/recoverSingleData:
    post:
      tags:
        - 数据恢复
      summary: 恢复单个数据
      description: 恢复单个已删除的数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SingleRecoveryRequest'
            example:
              dataType: "template"
              dataId: "123"
      responses:
        '200':
          description: 恢复成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BasicResponse'
              example:
                success: true
                code: "200"
                msg: "数据恢复成功"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/recoverBatchData:
    post:
      tags:
        - 数据恢复
      summary: 批量恢复数据
      description: 批量恢复多个已删除的数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchRecoveryRequest'
            example:
              dataType: "template"
              dataIds: "123,456,789"
      responses:
        '200':
          description: 批量恢复完成
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchRecoveryResponse'
              example:
                success: true
                code: "200"
                msg: "批量恢复成功"
                data:
                  successCount: 3
                  failCount: 0
                  failedIds: []
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/recoverDataByDateRange:
    post:
      tags:
        - 数据恢复
      summary: 按时间范围恢复数据
      description: 恢复指定时间范围内删除的数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DateRangeRecoveryRequest'
            example:
              dataType: "template"
              startDate: "2024-07-20"
              endDate: "2024-07-21"
      responses:
        '200':
          description: 时间范围恢复完成
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchRecoveryResponse'
              example:
                success: true
                code: "200"
                msg: "批量恢复成功"
                data:
                  successCount: 5
                  failCount: 1
                  failedIds: ["789"]
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/permanentDeleteData:
    post:
      tags:
        - 数据删除
      summary: 永久删除数据
      description: 永久删除已删除的数据（不可恢复）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SingleRecoveryRequest'
            example:
              dataType: "template"
              dataId: "123"
      responses:
        '200':
          description: 永久删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BasicResponse'
              example:
                success: true
                code: "200"
                msg: "数据永久删除成功"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/getRecoveryStatistics:
    get:
      tags:
        - 统计信息
      summary: 获取恢复统计信息
      description: 获取用户的数据恢复统计信息
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatisticsResponse'
              example:
                success: true
                code: "200"
                msg: "操作成功"
                statistics:
                  deletedTemplateCount: 15
                  deletedGroupCount: 3
                  deletedFileCount: 8
                  totalDeletedCount: 26
                  recentDeletedCount: 5
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

  /datarecovery/getRecoveryHistory:
    get:
      tags:
        - 统计信息
      summary: 获取恢复历史记录
      description: 分页获取用户的数据恢复操作历史记录
      parameters:
        - name: pageNumber
          in: query
          required: false
          description: 页码，默认1
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: pageSize
          in: query
          required: false
          description: 每页大小，默认10
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoryPageResponse'
              example:
                success: true
                code: "200"
                msg: "操作成功"
                page:
                  list:
                    - id: 1
                      userId: 1001
                      dataType: "template"
                      targetId: 123
                      targetName: "测试模板"
                      recoveryType: "single"
                      recoveryStatus: "success"
                      recoveryTime: "2024-07-21 10:30:00"
                      originalDeleteTime: "2024-07-20 15:30:00"
                  pageNumber: 1
                  pageSize: 10
                  totalPage: 1
                  totalRow: 1
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 用户认证Token

  schemas:
    # 基础响应结构
    BasicResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 操作是否成功
          example: true
        code:
          type: string
          description: 响应码
          example: "200"
        msg:
          type: string
          description: 响应消息
          example: "操作成功"
      required:
        - success
        - code
        - msg

    # 分页响应结构
    PageResponse:
      allOf:
        - $ref: '#/components/schemas/BasicResponse'
        - type: object
          properties:
            page:
              $ref: '#/components/schemas/PageData'

    # 分页数据结构
    PageData:
      type: object
      properties:
        list:
          type: array
          description: 数据列表
          items:
            type: object
        pageNumber:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页大小
          example: 10
        totalPage:
          type: integer
          description: 总页数
          example: 1
        totalRow:
          type: integer
          description: 总记录数
          example: 1

    # 数据详情响应
    DataDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BasicResponse'
        - type: object
          properties:
            data:
              type: object
              description: 数据详情
              properties:
                id:
                  type: integer
                  description: 数据ID
                name:
                  type: string
                  description: 数据名称
                createTime:
                  type: string
                  format: date-time
                  description: 创建时间
                deleteTime:
                  type: string
                  format: date-time
                  description: 删除时间
                userId:
                  type: integer
                  description: 用户ID

    # 单个恢复请求
    SingleRecoveryRequest:
      type: object
      properties:
        dataType:
          type: string
          enum: [template, template_group, cloudfile]
          description: 数据类型
          example: template
        dataId:
          type: string
          description: 数据ID
          example: "123"
      required:
        - dataType
        - dataId

    # 批量恢复请求
    BatchRecoveryRequest:
      type: object
      properties:
        dataType:
          type: string
          enum: [template, template_group, cloudfile]
          description: 数据类型
          example: template
        dataIds:
          type: string
          description: 数据ID列表，逗号分隔
          example: "123,456,789"
      required:
        - dataType
        - dataIds

    # 时间范围恢复请求
    DateRangeRecoveryRequest:
      type: object
      properties:
        dataType:
          type: string
          enum: [template, template_group, cloudfile]
          description: 数据类型
          example: template
        startDate:
          type: string
          format: date
          description: 开始日期
          example: "2024-07-20"
        endDate:
          type: string
          format: date
          description: 结束日期
          example: "2024-07-21"
      required:
        - dataType
        - startDate
        - endDate

    # 批量恢复响应
    BatchRecoveryResponse:
      allOf:
        - $ref: '#/components/schemas/BasicResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                successCount:
                  type: integer
                  description: 成功数量
                  example: 3
                failCount:
                  type: integer
                  description: 失败数量
                  example: 0
                failedIds:
                  type: array
                  items:
                    type: string
                  description: 失败的ID列表
                  example: []

    # 统计信息响应
    StatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/BasicResponse'
        - type: object
          properties:
            statistics:
              type: object
              properties:
                deletedTemplateCount:
                  type: integer
                  description: 已删除模板数量
                  example: 15
                deletedGroupCount:
                  type: integer
                  description: 已删除分组数量
                  example: 3
                deletedFileCount:
                  type: integer
                  description: 已删除文件数量
                  example: 8
                totalDeletedCount:
                  type: integer
                  description: 总删除数量
                  example: 26
                recentDeletedCount:
                  type: integer
                  description: 最近7天删除数量
                  example: 5

    # 历史记录分页响应
    HistoryPageResponse:
      allOf:
        - $ref: '#/components/schemas/BasicResponse'
        - type: object
          properties:
            page:
              type: object
              properties:
                list:
                  type: array
                  items:
                    $ref: '#/components/schemas/RecoveryHistoryItem'
                pageNumber:
                  type: integer
                  example: 1
                pageSize:
                  type: integer
                  example: 10
                totalPage:
                  type: integer
                  example: 1
                totalRow:
                  type: integer
                  example: 1

    # 恢复历史记录项
    RecoveryHistoryItem:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
          example: 1
        userId:
          type: integer
          description: 用户ID
          example: 1001
        dataType:
          type: string
          description: 数据类型
          example: template
        targetId:
          type: integer
          description: 目标数据ID
          example: 123
        targetName:
          type: string
          description: 目标数据名称
          example: "测试模板"
        recoveryType:
          type: string
          enum: [single, batch, batch_by_date]
          description: 恢复类型
          example: single
        recoveryStatus:
          type: string
          enum: [success, failed]
          description: 恢复状态
          example: success
        recoveryTime:
          type: string
          format: date-time
          description: 恢复时间
          example: "2024-07-21 10:30:00"
        originalDeleteTime:
          type: string
          format: date-time
          description: 原始删除时间
          example: "2024-07-20 15:30:00"
        errorMessage:
          type: string
          description: 错误信息（失败时）
          example: null

  responses:
    # 400 错误请求
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BasicResponse'
          example:
            success: false
            code: "501"
            msg: "参数不能为空"

    # 401 未授权
    Unauthorized:
      description: 用户未登录或token无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BasicResponse'
          example:
            success: false
            code: "501"
            msg: "用户未登录"

    # 404 资源不存在
    NotFound:
      description: 请求的资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BasicResponse'
          example:
            success: false
            code: "501"
            msg: "数据不存在或无权限访问"

    # 500 服务器内部错误
    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BasicResponse'
          example:
            success: false
            code: "501"
            msg: "服务器内部错误"

tags:
  - name: 数据查询
    description: 已删除数据的查询相关接口
  - name: 数据恢复
    description: 数据恢复相关接口
  - name: 数据删除
    description: 永久删除相关接口
  - name: 统计信息
    description: 统计信息和历史记录相关接口
