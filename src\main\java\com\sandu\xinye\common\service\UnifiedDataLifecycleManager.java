package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 统一数据生命周期管理器
 * 简化的2层数据管理策略：
 * - 0-6个月：可查询可恢复
 * - 6-12个月：可查询不可恢复（归档状态）
 * - 12个月后：永久删除
 * 
 * <AUTHOR> Team
 * @since 2024-07-21
 */
@Component
public class UnifiedDataLifecycleManager {

    // 简化的数据保留期限
    private static final int RECOVERY_PERIOD_DAYS = 180;  // 6个月可恢复期
    private static final int PURGE_PERIOD_DAYS = 365;     // 12个月后永久删除

    /**
     * 每天凌晨2点执行数据归档任务
     * 将超过6个月的已删除数据标记为归档状态
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void archiveExpiredData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行数据归档任务，批次ID: " + batchId);

        try {
            int totalArchived = 0;
            
            // 归档模板数据
            totalArchived += archiveDataByTable("templet", batchId);
            
            // 归档分组数据
            totalArchived += archiveDataByTable("templet_group", batchId);
            
            // 归档文件数据
            totalArchived += archiveDataByTable("cloudfile", batchId);

            LogKit.info("数据归档任务完成，批次ID: " + batchId + "，总计归档: " + totalArchived + " 条数据");
        } catch (Exception e) {
            LogKit.error("数据归档任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点执行数据永久删除任务
     * 永久删除超过12个月的数据
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void purgeExpiredData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行数据永久删除任务，批次ID: " + batchId);

        try {
            int totalPurged = 0;
            
            // 删除模板数据
            totalPurged += purgeDataByTable("templet", batchId);
            
            // 删除分组数据
            totalPurged += purgeDataByTable("templet_group", batchId);
            
            // 删除文件数据
            totalPurged += purgeDataByTable("cloudfile", batchId);

            LogKit.info("数据永久删除任务完成，批次ID: " + batchId + "，总计删除: " + totalPurged + " 条数据");
        } catch (Exception e) {
            LogKit.error("数据永久删除任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每周一上午9点发送数据即将过期提醒
     * 提前7天提醒用户数据即将无法恢复
     */
    @Scheduled(cron = "0 0 9 * * MON")
    public void sendExpirationNotifications() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始发送数据过期提醒，批次ID: " + batchId);

        try {
            // 查找即将过期的用户数据（还有7天就要归档）
            int warningDays = RECOVERY_PERIOD_DAYS - 7;  // 173天
            List<Record> usersWithExpiringData = findUsersWithExpiringData(warningDays);

            int notificationCount = 0;
            for (Record user : usersWithExpiringData) {
                try {
                    sendExpirationNotification(user);
                    logCleanupOperation(user.getInt("userId"), "notification", 0, 
                                     "数据过期提醒", "notify", 
                                     "即将过期数据: " + user.getInt("expiring_count") + "个", 
                                     batchId, 1);
                    notificationCount++;
                } catch (Exception e) {
                    LogKit.error("发送通知失败，用户ID: " + user.getInt("userId"), e);
                }
            }

            LogKit.info("数据过期提醒发送完成，批次ID: " + batchId + "，通知用户: " + notificationCount + " 个");
        } catch (Exception e) {
            LogKit.error("发送数据过期提醒失败: " + e.getMessage(), e);
        }
    }

    /**
     * 归档指定表的过期数据
     */
    private int archiveDataByTable(String tableName, String batchId) {
        String sql = "UPDATE " + tableName + " " +
                    "SET archiveTime = NOW() " +
                    "WHERE deleteTime IS NOT NULL " +
                    "AND archiveTime IS NULL " +
                    "AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        int affectedRows = Db.update(sql, RECOVERY_PERIOD_DAYS);

        if (affectedRows > 0) {
            LogKit.info("归档 " + tableName + " 表数据: " + affectedRows + " 条");
            logCleanupOperation(0, tableName, 0, "批量归档", "archive", 
                             "超过6个月的已删除数据", 
                             batchId, affectedRows);
        }

        return affectedRows;
    }

    /**
     * 永久删除超过12个月的已删除数据
     */
    private int purgeDataByTable(String tableName, String batchId) {
        // 执行删除
        String deleteSql = "DELETE FROM " + tableName + " " +
                          "WHERE deleteTime IS NOT NULL " +
                          "AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        int affectedRows = Db.update(deleteSql, PURGE_PERIOD_DAYS);

        if (affectedRows > 0) {
            LogKit.info("永久删除 " + tableName + " 表数据: " + affectedRows + " 条");
            
            // 记录删除日志（批量记录）
            logCleanupOperation(0, tableName, 0, "批量永久删除", "purge", 
                             "超过12个月的已删除数据", 
                             batchId, affectedRows);
        }

        return affectedRows;
    }

    /**
     * 查找有即将过期数据的用户
     */
    private List<Record> findUsersWithExpiringData(int warningDays) {
        String sql = "SELECT u.userId, u.userPhone, " +
                    "  (SELECT COUNT(*) FROM templet t WHERE t.userId = u.userId AND t.deleteTime IS NOT NULL AND t.archiveTime IS NULL AND t.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)) + " +
                    "  (SELECT COUNT(*) FROM templet_group tg WHERE tg.userId = u.userId AND tg.deleteTime IS NOT NULL AND tg.archiveTime IS NULL AND tg.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)) + " +
                    "  (SELECT COUNT(*) FROM cloudfile cf WHERE cf.userId = u.userId AND cf.deleteTime IS NOT NULL AND cf.archiveTime IS NULL AND cf.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)) " +
                    "  as expiring_count " +
                    "FROM user u " +
                    "HAVING expiring_count > 0";

        return Db.find(sql, warningDays, warningDays, warningDays);
    }

    /**
     * 发送过期通知
     */
    private void sendExpirationNotification(Record user) {
        LogKit.info("发送数据过期提醒给用户: " + user.getInt("userId") + 
                   ", 即将过期数据: " + user.getInt("expiring_count") + "个");
        
        // TODO: 实现具体的通知发送逻辑
        // 可以集成邮件服务、短信服务、站内消息等
        // MessageService.sendMessage(user.getInt("userId"), 
        //     "数据过期提醒", 
        //     "您有" + user.getInt("expiring_count") + "个已删除的文件将在7天后无法恢复，请及时处理。");
    }

    /**
     * 记录清理操作日志
     */
    private void logCleanupOperation(Integer userId, String dataType, Integer targetId, 
                                   String targetName, String operation, String reason, 
                                   String batchId, int affectedCount) {
        String sql = "INSERT INTO data_cleanup_logs " +
                    "(userId, dataType, targetId, targetName, operation, operationTime, reason, batchId, affectedCount) " +
                    "VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?)";

        Db.update(sql, userId, dataType, targetId, targetName, operation, reason, batchId, affectedCount);
    }

    /**
     * 手动触发数据清理（管理员功能）
     */
    public String manualCleanup(String operation) {
        String batchId = "MANUAL_" + UUID.randomUUID().toString();
        LogKit.info("手动触发数据清理: " + operation + ", 批次ID: " + batchId);

        try {
            int totalProcessed = 0;
            
            if ("archive".equals(operation)) {
                totalProcessed += archiveDataByTable("templet", batchId);
                totalProcessed += archiveDataByTable("templet_group", batchId);
                totalProcessed += archiveDataByTable("cloudfile", batchId);
                LogKit.info("手动归档完成，处理数据: " + totalProcessed + " 条");
            } else if ("purge".equals(operation)) {
                totalProcessed += purgeDataByTable("templet", batchId);
                totalProcessed += purgeDataByTable("templet_group", batchId);
                totalProcessed += purgeDataByTable("cloudfile", batchId);
                LogKit.info("手动永久删除完成，处理数据: " + totalProcessed + " 条");
            }
            
            return "操作成功，批次ID: " + batchId + "，处理数据: " + totalProcessed + " 条";
        } catch (Exception e) {
            LogKit.error("手动数据清理失败: " + e.getMessage(), e);
            throw new RuntimeException("数据清理失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据清理统计信息
     */
    public Record getCleanupStatistics() {
        String sql = "SELECT " +
                    "  SUM(CASE WHEN operation = 'archive' THEN affectedCount ELSE 0 END) as total_archived, " +
                    "  SUM(CASE WHEN operation = 'purge' THEN affectedCount ELSE 0 END) as total_purged, " +
                    "  COUNT(DISTINCT CASE WHEN operation = 'notify' THEN userId END) as notified_users, " +
                    "  MAX(operationTime) as last_operation_time " +
                    "FROM data_cleanup_logs " +
                    "WHERE operationTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)";

        return Db.findFirst(sql);
    }
}
