# 数据清理定时任务实现方案

## 📋 实现概述

基于用户等级实现自动化的数据生命周期管理，包括数据归档、清理和用户通知。

## 🗄️ 数据库结构调整

### 1. 添加生命周期管理字段

```sql
-- 为现有表添加数据生命周期字段
ALTER TABLE templet ADD COLUMN (
    dataStatus ENUM('active', 'deleted', 'archived') DEFAULT 'active',
    archiveTime DATETIME NULL,
    retentionExpiry DATETIME NULL,
    INDEX idx_data_status (dataStatus),
    INDEX idx_delete_time (deleteTime),
    INDEX idx_archive_time (archiveTime)
);

ALTER TABLE templet_group ADD COLUMN (
    dataStatus ENUM('active', 'deleted', 'archived') DEFAULT 'active',
    archiveTime DATETIME NULL,
    retentionExpiry DATETIME NULL,
    INDEX idx_data_status (dataStatus),
    INDEX idx_delete_time (deleteTime),
    INDEX idx_archive_time (archiveTime)
);

ALTER TABLE cloudfile ADD COLUMN (
    dataStatus ENUM('active', 'deleted', 'archived') DEFAULT 'active',
    archiveTime DATETIME NULL,
    retentionExpiry DATETIME NULL,
    INDEX idx_data_status (dataStatus),
    INDEX idx_delete_time (deleteTime),
    INDEX idx_archive_time (archiveTime)
);

-- 为用户表添加等级字段（如果不存在）
ALTER TABLE user ADD COLUMN userLevel ENUM('normal', 'vip', 'super_vip') DEFAULT 'normal';
```

### 2. 创建数据清理日志表

```sql
CREATE TABLE data_cleanup_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    dataType VARCHAR(50) NOT NULL,
    targetId INT NOT NULL,
    targetName VARCHAR(255),
    operation ENUM('archive', 'purge', 'notify') NOT NULL,
    operationTime DATETIME NOT NULL,
    reason VARCHAR(500),
    batchId VARCHAR(50),
    affectedCount INT DEFAULT 1,
    INDEX idx_user_time (userId, operationTime),
    INDEX idx_operation_time (operation, operationTime),
    INDEX idx_batch (batchId)
);
```

## ⚙️ Java 实现

### 1. 数据生命周期管理服务

```java
package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Component
public class DataLifecycleManager {

    // 用户等级对应的保留期限（天）
    private static final int NORMAL_RETENTION_DAYS = 30;
    private static final int VIP_RETENTION_DAYS = 180;  // 6个月
    private static final int SUPER_VIP_RETENTION_DAYS = 365;  // 12个月

    // 归档期限（保留期限的2倍）
    private static final int NORMAL_ARCHIVE_DAYS = 90;
    private static final int VIP_ARCHIVE_DAYS = 365;
    private static final int SUPER_VIP_ARCHIVE_DAYS = 730;

    /**
     * 每天凌晨2点执行数据归档任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void archiveExpiredData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行数据归档任务，批次ID: " + batchId);

        try {
            // 归档普通用户数据
            archiveUserDataByLevel("normal", NORMAL_RETENTION_DAYS, batchId);
            
            // 归档VIP用户数据
            archiveUserDataByLevel("vip", VIP_RETENTION_DAYS, batchId);
            
            // 归档超级VIP用户数据
            archiveUserDataByLevel("super_vip", SUPER_VIP_RETENTION_DAYS, batchId);

            LogKit.info("数据归档任务完成，批次ID: " + batchId);
        } catch (Exception e) {
            LogKit.error("数据归档任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点执行数据永久删除任务
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void purgeArchivedData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行数据永久删除任务，批次ID: " + batchId);

        try {
            // 删除普通用户归档数据
            purgeUserDataByLevel("normal", NORMAL_ARCHIVE_DAYS, batchId);
            
            // 删除VIP用户归档数据
            purgeUserDataByLevel("vip", VIP_ARCHIVE_DAYS, batchId);
            
            // 删除超级VIP用户归档数据
            purgeUserDataByLevel("super_vip", SUPER_VIP_ARCHIVE_DAYS, batchId);

            LogKit.info("数据永久删除任务完成，批次ID: " + batchId);
        } catch (Exception e) {
            LogKit.error("数据永久删除任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每天上午9点发送数据过期提醒
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendExpirationNotifications() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始发送数据过期提醒，批次ID: " + batchId);

        try {
            // 提前7天提醒
            sendNotificationsByUserLevel("normal", NORMAL_RETENTION_DAYS - 7, batchId);
            sendNotificationsByUserLevel("vip", VIP_RETENTION_DAYS - 7, batchId);
            sendNotificationsByUserLevel("super_vip", SUPER_VIP_RETENTION_DAYS - 7, batchId);

            LogKit.info("数据过期提醒发送完成，批次ID: " + batchId);
        } catch (Exception e) {
            LogKit.error("发送数据过期提醒失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据用户等级归档数据
     */
    private void archiveUserDataByLevel(String userLevel, int retentionDays, String batchId) {
        LogKit.info("开始归档 " + userLevel + " 用户数据，保留期限: " + retentionDays + " 天");

        // 归档模板数据
        int templateCount = archiveDataByTable("templet", userLevel, retentionDays, batchId);
        
        // 归档分组数据
        int groupCount = archiveDataByTable("templet_group", userLevel, retentionDays, batchId);
        
        // 归档文件数据
        int fileCount = archiveDataByTable("cloudfile", userLevel, retentionDays, batchId);

        LogKit.info(String.format("归档完成 - %s用户: 模板%d个, 分组%d个, 文件%d个", 
                   userLevel, templateCount, groupCount, fileCount));
    }

    /**
     * 根据表名归档数据
     */
    private int archiveDataByTable(String tableName, String userLevel, int retentionDays, String batchId) {
        String sql = "UPDATE " + tableName + " t " +
                    "JOIN user u ON t.userId = u.userId " +
                    "SET t.dataStatus = 'archived', t.archiveTime = NOW() " +
                    "WHERE u.userLevel = ? " +
                    "AND t.deleteTime IS NOT NULL " +
                    "AND t.dataStatus = 'deleted' " +
                    "AND t.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        int affectedRows = Db.update(sql, userLevel, retentionDays);

        // 记录归档日志
        if (affectedRows > 0) {
            logCleanupOperation(0, tableName, 0, "批量归档", "archive", 
                             "用户等级: " + userLevel + ", 保留期限: " + retentionDays + "天", 
                             batchId, affectedRows);
        }

        return affectedRows;
    }

    /**
     * 根据用户等级永久删除数据
     */
    private void purgeUserDataByLevel(String userLevel, int archiveDays, String batchId) {
        LogKit.info("开始永久删除 " + userLevel + " 用户归档数据，归档期限: " + archiveDays + " 天");

        // 删除模板数据
        int templateCount = purgeDataByTable("templet", userLevel, archiveDays, batchId);
        
        // 删除分组数据
        int groupCount = purgeDataByTable("templet_group", userLevel, archiveDays, batchId);
        
        // 删除文件数据
        int fileCount = purgeDataByTable("cloudfile", userLevel, archiveDays, batchId);

        LogKit.info(String.format("永久删除完成 - %s用户: 模板%d个, 分组%d个, 文件%d个", 
                   userLevel, templateCount, groupCount, fileCount));
    }

    /**
     * 根据表名永久删除数据
     */
    private int purgeDataByTable(String tableName, String userLevel, int archiveDays, String batchId) {
        // 先查询要删除的数据，用于日志记录
        String selectSql = "SELECT t.id, t.name, t.userId FROM " + tableName + " t " +
                          "JOIN user u ON t.userId = u.userId " +
                          "WHERE u.userLevel = ? " +
                          "AND t.dataStatus = 'archived' " +
                          "AND t.archiveTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        List<Record> toDelete = Db.find(selectSql, userLevel, archiveDays);

        if (toDelete.isEmpty()) {
            return 0;
        }

        // 执行删除
        String deleteSql = "DELETE t FROM " + tableName + " t " +
                          "JOIN user u ON t.userId = u.userId " +
                          "WHERE u.userLevel = ? " +
                          "AND t.dataStatus = 'archived' " +
                          "AND t.archiveTime < DATE_SUB(NOW(), INTERVAL ? DAY)";

        int affectedRows = Db.update(deleteSql, userLevel, archiveDays);

        // 记录删除日志
        for (Record record : toDelete) {
            logCleanupOperation(record.getInt("userId"), tableName, record.getInt("id"), 
                             record.getStr("name"), "purge", 
                             "用户等级: " + userLevel + ", 归档期限: " + archiveDays + "天", 
                             batchId, 1);
        }

        return affectedRows;
    }

    /**
     * 发送数据过期通知
     */
    private void sendNotificationsByUserLevel(String userLevel, int warningDays, String batchId) {
        String sql = "SELECT DISTINCT u.userId, u.userPhone, COUNT(*) as expiring_count " +
                    "FROM user u " +
                    "JOIN (" +
                    "  SELECT userId FROM templet WHERE deleteTime IS NOT NULL AND dataStatus = 'deleted' " +
                    "    AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY) " +
                    "  UNION ALL " +
                    "  SELECT userId FROM templet_group WHERE deleteTime IS NOT NULL AND dataStatus = 'deleted' " +
                    "    AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY) " +
                    "  UNION ALL " +
                    "  SELECT userId FROM cloudfile WHERE deleteTime IS NOT NULL AND dataStatus = 'deleted' " +
                    "    AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY) " +
                    ") expiring ON u.userId = expiring.userId " +
                    "WHERE u.userLevel = ? " +
                    "GROUP BY u.userId, u.userPhone";

        List<Record> users = Db.find(sql, warningDays, warningDays, warningDays, userLevel);

        for (Record user : users) {
            try {
                // 发送通知（邮件、短信、站内信等）
                sendExpirationNotification(user);
                
                // 记录通知日志
                logCleanupOperation(user.getInt("userId"), "notification", 0, 
                                 "数据过期提醒", "notify", 
                                 "即将过期数据: " + user.getInt("expiring_count") + "个", 
                                 batchId, 1);
            } catch (Exception e) {
                LogKit.error("发送通知失败，用户ID: " + user.getInt("userId"), e);
            }
        }
    }

    /**
     * 发送过期通知
     */
    private void sendExpirationNotification(Record user) {
        // TODO: 实现具体的通知发送逻辑
        // 可以集成邮件服务、短信服务、站内消息等
        LogKit.info("发送数据过期提醒给用户: " + user.getInt("userId") + 
                   ", 即将过期数据: " + user.getInt("expiring_count") + "个");
    }

    /**
     * 记录清理操作日志
     */
    private void logCleanupOperation(Integer userId, String dataType, Integer targetId, 
                                   String targetName, String operation, String reason, 
                                   String batchId, int affectedCount) {
        String sql = "INSERT INTO data_cleanup_logs " +
                    "(userId, dataType, targetId, targetName, operation, operationTime, reason, batchId, affectedCount) " +
                    "VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?)";

        Db.update(sql, userId, dataType, targetId, targetName, operation, reason, batchId, affectedCount);
    }

    /**
     * 手动触发数据清理（管理员功能）
     */
    public void manualCleanup(String userLevel, String operation) {
        String batchId = "MANUAL_" + UUID.randomUUID().toString();
        LogKit.info("手动触发数据清理: " + operation + ", 用户等级: " + userLevel);

        try {
            if ("archive".equals(operation)) {
                int retentionDays = getRetentionDays(userLevel);
                archiveUserDataByLevel(userLevel, retentionDays, batchId);
            } else if ("purge".equals(operation)) {
                int archiveDays = getArchiveDays(userLevel);
                purgeUserDataByLevel(userLevel, archiveDays, batchId);
            }
        } catch (Exception e) {
            LogKit.error("手动数据清理失败: " + e.getMessage(), e);
            throw e;
        }
    }

    private int getRetentionDays(String userLevel) {
        switch (userLevel) {
            case "vip": return VIP_RETENTION_DAYS;
            case "super_vip": return SUPER_VIP_RETENTION_DAYS;
            default: return NORMAL_RETENTION_DAYS;
        }
    }

    private int getArchiveDays(String userLevel) {
        switch (userLevel) {
            case "vip": return VIP_ARCHIVE_DAYS;
            case "super_vip": return SUPER_VIP_ARCHIVE_DAYS;
            default: return NORMAL_ARCHIVE_DAYS;
        }
    }
}
```

### 2. 配置类

```java
package com.sandu.xinye.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableScheduling
public class SchedulingConfig {
    // Spring Boot 自动配置定时任务
}
```

## 📊 监控和报告

### 1. 清理统计查询

```sql
-- 查看清理统计
SELECT 
    operation,
    dataType,
    DATE(operationTime) as operation_date,
    COUNT(*) as operation_count,
    SUM(affectedCount) as total_affected
FROM data_cleanup_logs 
WHERE operationTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY operation, dataType, DATE(operationTime)
ORDER BY operation_date DESC;

-- 查看用户数据清理情况
SELECT 
    u.userLevel,
    COUNT(DISTINCT dcl.userId) as affected_users,
    SUM(dcl.affectedCount) as total_cleaned_items
FROM data_cleanup_logs dcl
JOIN user u ON dcl.userId = u.userId
WHERE dcl.operationTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY u.userLevel;
```

### 2. 性能监控

```java
// 添加性能监控
@Component
public class DataCleanupMonitor {
    
    @EventListener
    public void onCleanupComplete(CleanupCompleteEvent event) {
        // 记录清理性能指标
        LogKit.info("清理任务完成 - 耗时: " + event.getDuration() + "ms, " +
                   "处理数据: " + event.getProcessedCount() + "条");
    }
}
```

这个实现方案提供了完整的数据生命周期管理功能，包括自动归档、清理和用户通知，同时保证了系统性能和数据安全。
