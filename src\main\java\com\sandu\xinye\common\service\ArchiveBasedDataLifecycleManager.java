package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 基于 archive_records 表的数据生命周期管理器
 * 简化的2层数据管理策略：
 * - 0-6个月：热存储（主表中 deleteTime != NULL）
 * - 6-12个月：冷存储（archive_records 表 + OSS）
 * - 12个月后：永久删除（清理 archive_records 和 OSS）
 * 
 * <AUTHOR> Team
 * @since 2024-07-21
 */
@Component
public class ArchiveBasedDataLifecycleManager {

    // 简化的数据保留期限
    private static final int RECOVERY_PERIOD_DAYS = 180;  // 6个月可恢复期
    private static final int PURGE_PERIOD_DAYS = 365;     // 12个月后永久删除

    /**
     * 每天凌晨2点执行数据归档任务
     * 将超过6个月的已删除数据归档到 archive_records 表和 OSS
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void archiveExpiredData() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行数据归档任务，批次ID: " + batchId);

        try {
            int totalArchived = 0;
            
            // 归档模板数据
            totalArchived += archiveDataByTable("templet", batchId);
            
            // 归档分组数据
            totalArchived += archiveDataByTable("templet_group", batchId);
            
            // 归档文件数据
            totalArchived += archiveDataByTable("cloudfile", batchId);

            LogKit.info("数据归档任务完成，批次ID: " + batchId + "，总计归档: " + totalArchived + " 条数据");
        } catch (Exception e) {
            LogKit.error("数据归档任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点执行数据永久删除任务
     * 永久删除超过12个月的归档数据
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void purgeExpiredArchives() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始执行归档数据永久删除任务，批次ID: " + batchId);

        try {
            // 查询需要删除的归档记录
            String selectSql = "SELECT id, archiveKey, dataType, originalId, userId " +
                              "FROM archive_records " +
                              "WHERE archiveTime < DATE_SUB(NOW(), INTERVAL ? DAY) " +
                              "AND status = 'archived'";
            
            List<Record> expiredArchives = Db.find(selectSql, PURGE_PERIOD_DAYS);
            
            int deletedCount = 0;
            for (Record archive : expiredArchives) {
                try {
                    // 删除 OSS 文件
                    String archiveKey = archive.getStr("archiveKey");
                    deleteFromOSS(archiveKey);
                    
                    // 删除归档记录
                    Db.deleteById("archive_records", archive.getLong("id"));
                    
                    deletedCount++;
                    
                    // 记录删除日志
                    logCleanupOperation(archive.getInt("userId"), archive.getStr("dataType"), 
                                     archive.getInt("originalId"), "永久删除归档", "purge", 
                                     "超过12个月的归档数据", batchId, 1);
                    
                } catch (Exception e) {
                    LogKit.error("删除归档记录失败: " + archive.getLong("id"), e);
                }
            }

            LogKit.info("归档数据永久删除任务完成，批次ID: " + batchId + "，总计删除: " + deletedCount + " 条归档");
        } catch (Exception e) {
            LogKit.error("归档数据永久删除任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每周一上午9点发送数据即将过期提醒
     */
    @Scheduled(cron = "0 0 9 * * MON")
    public void sendExpirationNotifications() {
        String batchId = UUID.randomUUID().toString();
        LogKit.info("开始发送数据过期提醒，批次ID: " + batchId);

        try {
            // 查找即将过期的用户数据（还有7天就要归档）
            int warningDays = RECOVERY_PERIOD_DAYS - 7;  // 173天
            List<Record> usersWithExpiringData = findUsersWithExpiringData(warningDays);

            int notificationCount = 0;
            for (Record user : usersWithExpiringData) {
                try {
                    sendExpirationNotification(user);
                    logCleanupOperation(user.getInt("userId"), "notification", 0, 
                                     "数据过期提醒", "notify", 
                                     "即将过期数据: " + user.getInt("expiring_count") + "个", 
                                     batchId, 1);
                    notificationCount++;
                } catch (Exception e) {
                    LogKit.error("发送通知失败，用户ID: " + user.getInt("userId"), e);
                }
            }

            LogKit.info("数据过期提醒发送完成，批次ID: " + batchId + "，通知用户: " + notificationCount + " 个");
        } catch (Exception e) {
            LogKit.error("发送数据过期提醒失败: " + e.getMessage(), e);
        }
    }

    /**
     * 归档指定表的过期数据到 archive_records 表和 OSS
     */
    private int archiveDataByTable(String tableName, String batchId) {
        // 查询需要归档的数据
        String selectSql = "SELECT * FROM " + tableName + " " +
                          "WHERE deleteTime IS NOT NULL " +
                          "AND deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)";
        
        List<Record> dataToArchive = Db.find(selectSql, RECOVERY_PERIOD_DAYS);
        
        if (dataToArchive.isEmpty()) {
            return 0;
        }

        int archivedCount = 0;
        for (Record data : dataToArchive) {
            try {
                // 上传到 OSS
                String archiveKey = uploadToOSS(tableName, data);
                
                // 创建归档记录
                Record archiveRecord = new Record();
                archiveRecord.set("dataType", getDataType(tableName));
                archiveRecord.set("originalId", data.get("id"));
                archiveRecord.set("userId", data.get("userId"));
                archiveRecord.set("archiveKey", archiveKey);
                archiveRecord.set("archiveSize", calculateDataSize(data));
                archiveRecord.set("archiveTime", new java.util.Date());
                archiveRecord.set("deleteTime", data.get("deleteTime"));
                archiveRecord.set("metadata", buildMetadata(data));
                archiveRecord.set("status", "archived");
                
                Db.save("archive_records", archiveRecord);
                
                // 从主表删除数据
                Db.deleteById(tableName, data.get("id"));
                
                archivedCount++;
                
            } catch (Exception e) {
                LogKit.error("归档数据失败: " + tableName + " ID=" + data.get("id"), e);
            }
        }

        if (archivedCount > 0) {
            LogKit.info("归档 " + tableName + " 表数据: " + archivedCount + " 条");
            logCleanupOperation(0, tableName, 0, "批量归档", "archive", 
                             "超过6个月的已删除数据", batchId, archivedCount);
        }

        return archivedCount;
    }

    /**
     * 查找有即将过期数据的用户
     */
    private List<Record> findUsersWithExpiringData(int warningDays) {
        String sql = "SELECT u.userId, u.userPhone, " +
                    "  (SELECT COUNT(*) FROM templet t WHERE t.userId = u.userId AND t.deleteTime IS NOT NULL AND t.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)) + " +
                    "  (SELECT COUNT(*) FROM templet_group tg WHERE tg.userId = u.userId AND tg.deleteTime IS NOT NULL AND tg.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)) + " +
                    "  (SELECT COUNT(*) FROM cloudfile cf WHERE cf.userId = u.userId AND cf.deleteTime IS NOT NULL AND cf.deleteTime < DATE_SUB(NOW(), INTERVAL ? DAY)) " +
                    "  as expiring_count " +
                    "FROM user u " +
                    "HAVING expiring_count > 0";

        return Db.find(sql, warningDays, warningDays, warningDays);
    }

    /**
     * 发送过期通知
     */
    private void sendExpirationNotification(Record user) {
        LogKit.info("发送数据过期提醒给用户: " + user.getInt("userId") + 
                   ", 即将过期数据: " + user.getInt("expiring_count") + "个");
        
        // TODO: 实现具体的通知发送逻辑
    }

    /**
     * 上传数据到 OSS
     */
    private String uploadToOSS(String tableName, Record data) {
        // TODO: 实现 OSS 上传逻辑
        // 生成唯一的存储键
        String archiveKey = String.format("archive/%s/%s/%d_%d.json", 
                                         tableName, 
                                         java.time.LocalDate.now().toString(),
                                         data.get("userId"),
                                         data.get("id"));
        
        // 将数据序列化为 JSON 并上传到 OSS
        // OSSClient.putObject(bucketName, archiveKey, data.toJson());
        
        return archiveKey;
    }

    /**
     * 从 OSS 删除文件
     */
    private void deleteFromOSS(String archiveKey) {
        // TODO: 实现 OSS 删除逻辑
        // OSSClient.deleteObject(bucketName, archiveKey);
        LogKit.info("删除 OSS 文件: " + archiveKey);
    }

    /**
     * 计算数据大小
     */
    private Long calculateDataSize(Record data) {
        // 简单估算数据大小
        return (long) data.toJson().length();
    }

    /**
     * 构建元数据
     */
    private String buildMetadata(Record data) {
        // 保存关键元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("originalData", data.getColumns());
        metadata.put("archiveVersion", "1.0");
        metadata.put("archiveTime", new java.util.Date());
        
        return com.jfinal.kit.JsonKit.toJson(metadata);
    }

    /**
     * 获取数据类型
     */
    private String getDataType(String tableName) {
        switch (tableName) {
            case "templet": return "template";
            case "templet_group": return "template_group";
            case "cloudfile": return "cloudfile";
            default: return tableName;
        }
    }

    /**
     * 记录清理操作日志
     */
    private void logCleanupOperation(Integer userId, String dataType, Integer targetId, 
                                   String targetName, String operation, String reason, 
                                   String batchId, int affectedCount) {
        String sql = "INSERT INTO data_cleanup_logs " +
                    "(userId, dataType, targetId, targetName, operation, operationTime, reason, batchId, affectedCount) " +
                    "VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?)";

        Db.update(sql, userId, dataType, targetId, targetName, operation, reason, batchId, affectedCount);
    }

    /**
     * 手动触发数据清理（管理员功能）
     */
    public String manualCleanup(String operation) {
        String batchId = "MANUAL_" + UUID.randomUUID().toString();
        LogKit.info("手动触发数据清理: " + operation + ", 批次ID: " + batchId);

        try {
            int totalProcessed = 0;
            
            if ("archive".equals(operation)) {
                totalProcessed += archiveDataByTable("templet", batchId);
                totalProcessed += archiveDataByTable("templet_group", batchId);
                totalProcessed += archiveDataByTable("cloudfile", batchId);
                LogKit.info("手动归档完成，处理数据: " + totalProcessed + " 条");
            } else if ("purge".equals(operation)) {
                // 手动清理归档数据
                purgeExpiredArchives();
                LogKit.info("手动清理归档数据完成");
            }
            
            return "操作成功，批次ID: " + batchId + "，处理数据: " + totalProcessed + " 条";
        } catch (Exception e) {
            LogKit.error("手动数据清理失败: " + e.getMessage(), e);
            throw new RuntimeException("数据清理失败: " + e.getMessage());
        }
    }
}
