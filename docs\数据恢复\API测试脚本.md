# 数据恢复API测试脚本

## 测试环境准备

1. 确保数据库已执行软删除功能测试脚本
2. 确保有测试用户和已删除的测试数据
3. 获取有效的用户登录token

## 测试用例

### 1. 获取已删除模板列表

```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedDataList" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "pageNumber": 1,
    "pageSize": 10
  }'
```

### 2. 获取已删除模板详情

```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedDataDetail" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "dataId": "5000000"
  }'
```

### 3. 恢复单个模板

```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverSingleData" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "dataId": "5000000"
  }'
```

### 4. 批量恢复模板

```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverBatchData" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "dataIds": "5000000,5000001,5000002"
  }'
```

### 5. 按时间范围恢复数据

```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverDataByDateRange" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "startDate": "2024-07-20",
    "endDate": "2024-07-21"
  }'
```

### 6. 获取恢复统计信息

```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getRecoveryStatistics" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 7. 获取恢复历史记录

```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getRecoveryHistory" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNumber": 1,
    "pageSize": 10
  }'
```

### 8. 永久删除数据

```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/permanentDeleteData" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "dataId": "5000000"
  }'
```

## 测试模板分组

### 获取已删除分组列表

```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedDataList" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template_group",
    "pageNumber": 1,
    "pageSize": 10
  }'
```

### 恢复模板分组

```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverSingleData" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template_group",
    "dataId": "100000"
  }'
```

## 测试云端文件

### 获取已删除文件列表

```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedDataList" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "cloudfile",
    "pageNumber": 1,
    "pageSize": 10
  }'
```

### 恢复云端文件

```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverSingleData" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "cloudfile",
    "dataId": "10000"
  }'
```

## 预期测试结果

### 成功响应示例

```json
{
  "success": true,
  "code": "200",
  "msg": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 失败响应示例

```json
{
  "success": false,
  "code": "501",
  "msg": "具体错误信息"
}
```

## 测试注意事项

1. **认证**: 确保使用有效的用户token
2. **权限**: 只能操作当前用户的数据
3. **数据状态**: 只有已删除的数据才能恢复
4. **ID有效性**: 使用实际存在的数据ID进行测试
5. **时间格式**: 日期参数使用 yyyy-MM-dd 格式
6. **批量操作**: 注意ID列表的格式（逗号分隔）

## 测试流程建议

1. 首先获取已删除数据列表，确认有测试数据
2. 获取具体数据详情，验证数据完整性
3. 执行单个恢复操作，验证恢复功能
4. 重新删除数据，准备批量测试
5. 执行批量恢复操作，验证批量功能
6. 查看恢复历史记录，验证日志功能
7. 获取统计信息，验证统计功能
8. 最后测试永久删除功能（谨慎操作）

## 错误排查

如果测试失败，检查以下项目：

1. 数据库连接是否正常
2. 软删除字段是否已添加
3. 测试数据是否存在且已被软删除
4. 用户认证是否有效
5. 参数格式是否正确
6. 服务器日志中的具体错误信息
