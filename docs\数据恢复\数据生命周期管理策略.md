# XPrinter 数据生命周期管理策略

## 📋 概述

基于用户等级和业务需求，设计分层的数据保留和清理策略，平衡用户体验与存储成本。

## 🎯 设计目标

1. **用户体验**: 为不同等级用户提供差异化服务
2. **成本控制**: 合理控制存储和维护成本
3. **合规安全**: 满足数据保护法规要求
4. **性能优化**: 保持系统查询和恢复性能

## 📊 用户等级与保留期限

### 数据保留策略
| 用户等级 | 可恢复期限 | 归档期限 | 永久删除 |
|----------|------------|----------|----------|
| 普通用户 | 30天 | 90天 | 6个月 |
| VIP用户 | 6个月 | 12个月 | 18个月 |
| 超级VIP | 12个月 | 24个月 | 36个月 |

### 存储分层
```
🔥 热存储 (0-30天)
├── 实时查询和恢复
├── 高性能SSD存储
└── 完整功能支持

🌡️ 温存储 (30天-保留期限)
├── 正常查询和恢复
├── 标准存储
└── 完整功能支持

❄️ 冷存储 (保留期限-归档期限)
├── 只读查询，不可恢复
├── 低成本存储
└── 仅供查看和导出

🗑️ 永久删除 (超过归档期限)
├── 数据完全清除
├── 不可恢复
└── 释放存储空间
```

## 🔄 数据状态流转

### 1. 软删除阶段 (deleteTime设置)
```sql
-- 用户删除操作，设置删除时间
UPDATE templet SET deleteTime = NOW() WHERE id = ?;
```

### 2. 热存储阶段 (0-30天)
- **状态**: `deleted`
- **功能**: 完整的查询和恢复功能
- **存储**: 主数据库，高性能存储
- **用户体验**: 秒级恢复

### 3. 温存储阶段 (30天-保留期限)
- **状态**: `deleted`
- **功能**: 正常查询和恢复功能
- **存储**: 主数据库，标准存储
- **用户体验**: 正常恢复速度

### 4. 冷存储阶段 (保留期限-归档期限)
```sql
-- 添加归档状态字段
ALTER TABLE templet ADD COLUMN archiveTime DATETIME NULL;
ALTER TABLE templet_group ADD COLUMN archiveTime DATETIME NULL;
ALTER TABLE cloudfile ADD COLUMN archiveTime DATETIME NULL;

-- 数据归档操作
UPDATE templet SET archiveTime = NOW() 
WHERE deleteTime IS NOT NULL 
  AND deleteTime < DATE_SUB(NOW(), INTERVAL 6 MONTH)
  AND archiveTime IS NULL;
```

- **状态**: `archived`
- **功能**: 只读查询，不可恢复
- **存储**: 归档数据库或对象存储
- **用户体验**: 可查看历史记录，支持导出

### 5. 永久删除阶段
```sql
-- 永久删除超过归档期限的数据
DELETE FROM templet 
WHERE archiveTime IS NOT NULL 
  AND archiveTime < DATE_SUB(NOW(), INTERVAL 12 MONTH);
```

## ⚙️ 技术实现

### 1. 数据库结构调整
```sql
-- 添加数据生命周期管理字段
ALTER TABLE templet ADD COLUMN dataStatus ENUM('active', 'deleted', 'archived', 'purged') DEFAULT 'active';
ALTER TABLE templet ADD COLUMN retentionExpiry DATETIME NULL;
ALTER TABLE templet ADD COLUMN archiveTime DATETIME NULL;

-- 添加用户等级字段（如果不存在）
ALTER TABLE user ADD COLUMN userLevel ENUM('normal', 'vip', 'super_vip') DEFAULT 'normal';

-- 创建数据清理日志表
CREATE TABLE data_cleanup_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    dataType VARCHAR(50) NOT NULL,
    targetId INT NOT NULL,
    targetName VARCHAR(255),
    operation ENUM('archive', 'purge') NOT NULL,
    operationTime DATETIME NOT NULL,
    reason VARCHAR(500),
    INDEX idx_user_time (userId, operationTime),
    INDEX idx_operation_time (operation, operationTime)
);
```

### 2. 定时清理任务
```java
@Component
public class DataLifecycleManager {
    
    @Scheduled(cron = "0 2 * * * ?") // 每天凌晨2点执行
    public void archiveExpiredData() {
        // 归档超过保留期限的数据
        archiveDataByUserLevel();
    }
    
    @Scheduled(cron = "0 3 * * * ?") // 每天凌晨3点执行
    public void purgeArchivedData() {
        // 永久删除超过归档期限的数据
        purgeDataByUserLevel();
    }
    
    private void archiveDataByUserLevel() {
        // VIP用户：6个月后归档
        archiveUserData("vip", 6);
        // 普通用户：30天后归档
        archiveUserData("normal", 1);
    }
}
```

### 3. API接口调整
```java
// 查询接口需要过滤归档数据
public RetKit getDeletedDataList(Integer userId, String dataType, ...) {
    // 只查询未归档的删除数据
    String sql = "SELECT * FROM " + tableName + 
                " WHERE userId = ? AND deleteTime IS NOT NULL" +
                " AND (archiveTime IS NULL OR archiveTime > ?)";
    
    // 根据用户等级确定查询范围
    Date cutoffDate = calculateCutoffDate(userId);
    // ...
}
```

## 📢 用户通知机制

### 1. 数据即将过期提醒
```java
@Scheduled(cron = "0 9 * * * ?") // 每天上午9点检查
public void sendExpirationNotifications() {
    // 提前7天提醒用户数据即将过期
    List<User> users = findUsersWithExpiringData(7);
    for (User user : users) {
        sendExpirationNotification(user);
    }
}
```

### 2. 通知内容设计
```
📧 数据过期提醒邮件模板：

亲爱的用户，

您有 X 个已删除的设计文件将在 7 天后无法恢复：
- 模板文件：5个
- 分组：2个  
- 云端文件：3个

如需恢复这些文件，请在 YYYY-MM-DD 前登录系统操作。

💎 升级VIP可延长数据保留期至6个月
🔗 立即恢复：[恢复链接]
🔗 升级VIP：[升级链接]
```

## 💰 商业化策略

### 1. 差异化服务
- **普通用户**: 30天恢复期，激励升级VIP
- **VIP用户**: 6个月恢复期，核心价值体现
- **超级VIP**: 12个月恢复期，高端用户服务

### 2. 付费延长服务
```java
// 数据保留期延长服务
public class DataRetentionExtensionService {
    
    // 付费延长数据保留期
    public RetKit extendRetention(Integer userId, String dataIds, int months) {
        // 验证用户权限和付费状态
        // 延长指定数据的保留期限
        // 记录付费延长日志
    }
}
```

### 3. 数据导出服务
- 归档数据提供付费导出服务
- 批量下载历史设计文件
- 数据迁移和备份服务

## 🔒 合规和安全

### 1. 数据保护合规
- 遵循GDPR等数据保护法规
- 用户数据删除权利
- 数据处理透明度

### 2. 审计日志
```sql
-- 完整的数据操作审计
CREATE TABLE data_audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    userId INT NOT NULL,
    operation VARCHAR(50) NOT NULL,
    dataType VARCHAR(50) NOT NULL,
    targetId INT NOT NULL,
    oldStatus VARCHAR(20),
    newStatus VARCHAR(20),
    operationTime DATETIME NOT NULL,
    operatorType ENUM('user', 'system') NOT NULL,
    reason VARCHAR(500),
    ipAddress VARCHAR(45),
    userAgent TEXT
);
```

## 📈 监控和优化

### 1. 关键指标监控
- 数据恢复率
- 存储成本变化
- 用户满意度
- 系统性能影响

### 2. 自动化报告
```java
@Scheduled(cron = "0 8 1 * * ?") // 每月1号上午8点
public void generateMonthlyReport() {
    // 生成数据生命周期管理月报
    // 包含：清理数据量、成本节省、用户反馈等
}
```

## 🎯 实施建议

### 阶段一：基础功能 (1-2周)
1. 数据库结构调整
2. 基础的定时清理任务
3. 简单的用户通知

### 阶段二：完善功能 (2-3周)
1. 分层存储实现
2. 用户等级差异化
3. 付费延长服务

### 阶段三：优化提升 (1-2周)
1. 性能优化
2. 监控报告
3. 用户体验优化

这个策略既保证了用户体验，又有效控制了存储成本，同时为商业化提供了空间。
