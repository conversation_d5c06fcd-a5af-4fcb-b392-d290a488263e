# XPrinter 简化数据管理策略总结

## 📋 策略概述

基于简化的2层数据管理，为所有用户提供统一的数据生命周期管理服务。

## 🎯 核心设计

### 数据生命周期（2层结构）
```
📊 统一策略：
├── 0-6个月: 可查询可恢复（热存储）
├── 6-12个月: 可查询不可恢复（归档状态）
└── 12个月后: 永久删除（定期清理）
```

### 永久删除触发场景
1. **用户主动删除**: 用户在恢复界面手动永久删除
2. **系统自动清理**: 超过12个月的数据自动永久删除
3. **存储优化**: 定期清理释放存储空间
4. **管理员操作**: 管理员手动触发清理任务

## 🗄️ 技术实现

### 1. 数据库结构
```sql
-- 只需添加一个归档时间字段
ALTER TABLE templet ADD COLUMN archiveTime DATETIME NULL;
ALTER TABLE templet_group ADD COLUMN archiveTime DATETIME NULL;
ALTER TABLE cloudfile ADD COLUMN archiveTime DATETIME NULL;
```

### 2. 数据状态判断
```sql
-- 可恢复数据（0-6个月）
WHERE deleteTime IS NOT NULL AND archiveTime IS NULL

-- 归档数据（6-12个月）
WHERE deleteTime IS NOT NULL AND archiveTime IS NOT NULL

-- 永久删除（12个月后）
WHERE deleteTime < DATE_SUB(NOW(), INTERVAL 365 DAY)
```

### 3. 定时任务
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点归档
public void archiveExpiredData() {
    // 将超过6个月的数据标记为归档
}

@Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点清理
public void purgeExpiredData() {
    // 永久删除超过12个月的数据
}

@Scheduled(cron = "0 0 9 * * MON") // 每周一上午9点提醒
public void sendExpirationNotifications() {
    // 提前7天提醒用户数据即将无法恢复
}
```

## 🔧 API接口调整

### 1. 查询接口过滤
```java
// 只查询未归档的数据（6个月内可恢复）
sql.append("WHERE userId = ? AND deleteTime IS NOT NULL ");
sql.append("AND archiveTime IS NULL ");
```

### 2. 恢复接口验证
```java
// 检查数据是否已归档
if (template.get("archiveTime") != null) {
    return RetKit.fail("数据已归档无法恢复");
}
```

### 3. 统计信息增强
```json
{
  "deletedTemplateCount": 15,    // 可恢复的模板数量
  "deletedGroupCount": 3,        // 可恢复的分组数量
  "deletedFileCount": 8,         // 可恢复的文件数量
  "totalDeletedCount": 26,       // 可恢复总数量
  
  "archivedTemplateCount": 5,    // 归档的模板数量
  "archivedGroupCount": 2,       // 归档的分组数量
  "archivedFileCount": 3,        // 归档的文件数量
  "totalArchivedCount": 10,      // 归档总数量
  
  "recentDeletedCount": 5        // 最近7天删除数量
}
```

## 📢 用户体验设计

### 1. 界面提示
```
🔥 可恢复数据 (0-6个月)
├── 显示"恢复"按钮
├── 支持批量恢复
└── 正常的查询和操作

❄️ 归档数据 (6-12个月)
├── 显示"已归档"标签
├── 只能查看，不能恢复
└── 提供"查看历史记录"功能
```

### 2. 过期提醒
```
📧 提醒内容：
"您有 X 个已删除的文件将在7天后无法恢复：
- 模板文件：5个
- 分组：2个  
- 云端文件：3个

请在 YYYY-MM-DD 前及时恢复需要的文件。"
```

## ⚙️ 管理员功能

### 1. 手动清理接口
```java
// 手动触发归档
POST /api/v2/datarecovery/manualCleanup
{
  "operation": "archive"
}

// 手动触发永久删除
POST /api/v2/datarecovery/manualCleanup
{
  "operation": "purge"
}
```

### 2. 清理统计
```java
// 获取清理统计信息
GET /api/v2/datarecovery/getCleanupStatistics

// 响应示例
{
  "total_archived": 1250,      // 总归档数量
  "total_purged": 890,         // 总删除数量
  "notified_users": 45,        // 通知用户数量
  "last_operation_time": "2024-07-21 03:00:00"
}
```

## 📊 监控指标

### 1. 存储效果
```sql
-- 查看存储空间分布
SELECT 
  '可恢复数据' as category,
  COUNT(*) as count,
  ROUND(SUM(LENGTH(COALESCE(data, '')))/1024/1024, 2) as size_mb
FROM templet 
WHERE deleteTime IS NOT NULL AND archiveTime IS NULL
UNION ALL
SELECT 
  '归档数据' as category,
  COUNT(*) as count,
  ROUND(SUM(LENGTH(COALESCE(data, '')))/1024/1024, 2) as size_mb
FROM templet 
WHERE archiveTime IS NOT NULL;
```

### 2. 清理效果
```sql
-- 查看最近30天清理统计
SELECT 
  operation,
  DATE(operationTime) as operation_date,
  SUM(affectedCount) as daily_count
FROM data_cleanup_logs 
WHERE operationTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY operation, DATE(operationTime)
ORDER BY operation_date DESC;
```

## 🎯 实施优势

### 1. 简化管理
- ✅ 统一的用户策略，无需区分用户等级
- ✅ 简化的2层结构，易于理解和维护
- ✅ 自动化的生命周期管理

### 2. 成本控制
- ✅ 6个月后数据归档，减少查询负担
- ✅ 12个月后自动清理，释放存储空间
- ✅ 定期清理任务，避免数据无限增长

### 3. 用户体验
- ✅ 6个月的恢复期，满足大部分用户需求
- ✅ 归档数据仍可查看，保留历史记录
- ✅ 提前7天提醒，避免意外丢失

### 4. 技术优势
- ✅ 简单的数据库结构调整
- ✅ 高效的定时任务执行
- ✅ 完整的操作日志记录
- ✅ 灵活的管理员控制

## 📅 实施计划

### 第一阶段（1周）
1. 数据库结构调整
2. 定时任务实现
3. API接口调整

### 第二阶段（3天）
1. 用户通知机制
2. 管理员清理接口
3. 统计监控功能

### 第三阶段（2天）
1. 前端界面调整
2. 用户提醒功能
3. 测试和优化

## 💡 总结

这个简化的2层数据管理策略：

1. **用户友好**: 6个月恢复期满足大部分需求
2. **成本可控**: 自动清理避免存储无限增长
3. **技术简单**: 最小化的数据库和代码调整
4. **维护方便**: 统一策略，无需复杂的用户等级管理

既保证了用户体验，又有效控制了存储成本，是一个平衡且实用的解决方案。
