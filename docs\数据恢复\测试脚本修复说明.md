# 软删除功能测试脚本修复说明

## 问题描述

执行 `docs/数据恢复/软删除功能测试脚本.sql` 时出现错误：
```
Error Code: 1054. Unknown column 'phone' in 'field list'
```

## 问题原因

测试脚本中使用了错误的字段名 `phone`，但实际的 `user` 表中字段名为 `userPhone`。

## 修复内容

### 1. 字段名修正
- **修复前**：`INSERT IGNORE INTO user (userId, phone, createTime)`
- **修复后**：`INSERT IGNORE INTO user (userId, userPhone, status, registerType, createTime)`

### 2. 添加必要字段
根据 `BaseUser.java` 模型分析，添加了以下必要字段：
- `status`：用户状态（1-已注册，2-已登录）
- `registerType`：注册类型（0-QQ，1-微信，2-手机等）

### 3. 添加表结构预检查
在脚本开头添加了表结构检查部分：
```sql
-- 检查 user 表的字段结构，确保字段名正确
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许NULL',
    COLUMN_DEFAULT as '默认值'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user'
  AND COLUMN_NAME IN ('userId', 'userPhone', 'status', 'registerType', 'createTime')
ORDER BY ORDINAL_POSITION;
```

## 修复后的INSERT语句

```sql
-- 插入测试用户（如果不存在）
-- 添加必要的字段：status（状态）和 registerType（注册类型）
INSERT IGNORE INTO `user` (`userId`, `userPhone`, `status`, `registerType`, `createTime`) 
VALUES (9999, '13800000000', 1, 2, NOW());
```

## 字段值说明

- `userId`: 9999 - 测试用户ID
- `userPhone`: '13800000000' - 测试手机号
- `status`: 1 - 已注册状态
- `registerType`: 2 - 手机注册类型
- `createTime`: NOW() - 当前时间

## 验证方法

1. 执行修复后的脚本前，先运行表结构检查部分
2. 确认所有必需字段都存在且类型正确
3. 如果字段不存在或类型不匹配，需要先检查数据库schema

## 相关文件

- 修复的脚本：`docs/数据恢复/软删除功能测试脚本.sql`
- 用户模型：`src/main/java/com/sandu/xinye/common/model/base/BaseUser.java`
- 表结构检查脚本：`docs/数据恢复/检查user表结构.sql`
